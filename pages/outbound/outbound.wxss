/* pages/outbound/outbound.wxss */

/* ==================== 基础布局 ==================== */
.outbound-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  flex: 1;
  min-height: 0;
}

/* ==================== 日期选择区域 ==================== */
.date-selection-section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex-shrink: 0;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #2c9678 0%, #3ba688 100%);
  color: white;
}

.date-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.selected-date-display {
  font-size: 28rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.calendar-wrapper {
  padding: 24rpx;
  display: flex;
  justify-content: center;
}

.date-select-btn {
  background: linear-gradient(135deg, #2c9678 0%, #3ba688 100%) !important;
  border: none !important;
  color: white !important;
  border-radius: 24rpx !important;
  font-weight: 600 !important;
}


/* ==================== 出库记录列表区域 ==================== */
.outbound-records-section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
  flex-shrink: 0;
}

.records-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.records-stats {
  font-size: 24rpx;
  color: #666;
  background: #e8f5f1;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.records-list {
  flex: 1;
  padding: 20rpx;
  min-height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.empty-state {
  padding: 80rpx 0;
  text-align: center;
}

/* ==================== 出库记录卡片 ==================== */
.record-card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1rpx solid #f0f0f0;
}

.record-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid #eee;
}

.card-title {
  flex: 1;
}

.warehouse-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.record-time {
  font-size: 24rpx;
  color: #666;
}

.card-actions {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.card-content {
  padding: 24rpx;
}


/* ==================== 服装信息区域 ==================== */
.clothing-info-section {
  margin-bottom: 20rpx;
}

.clothing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  border: 1rpx solid #e9ecef;
}

.clothing-item:last-child {
  margin-bottom: 0;
}

.clothing-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.clothing-name.clickable {
  color: #2c9678;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #1e6b5c;
}

.clothing-quantity {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
}

/* ==================== 统计信息区域 ==================== */
.summary-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #e8f5f1 0%, #f0f9f6 100%);
  border-radius: 12rpx;
  border: 1rpx solid #d4edda;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c9678;
}

/* ==================== 底部操作按钮区域 ==================== */
.bottom-actions-section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
  flex-shrink: 0;
}

.actions-container {
  display: flex;
  gap: 16rpx;
  justify-content: center;
  align-items: center;
}

.action-button {
  flex: 1;
  max-width: 200rpx;
  height: 80rpx !important;
  border-radius: 24rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.primary-button {
  background: linear-gradient(135deg, #2c9678 0%, #3ba688 100%) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4rpx 12rpx rgba(44, 150, 120, 0.3) !important;
}

.primary-button:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(44, 150, 120, 0.4) !important;
}

.secondary-button {
  background: #f8f9fa !important;
  border: 1rpx solid #e9ecef !important;
  color: #666 !important;
}

.secondary-button:active {
  background: #e9ecef !important;
}

/* ==================== 弹窗样式 ==================== */
.clothing-popup-container {
  width: 100%;
  background: transparent;
}

.edit-dialog {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.dialog-content {
  padding: 24rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.form-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.dialog-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
}




