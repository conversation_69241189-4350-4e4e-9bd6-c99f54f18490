<!-- pages/outbound/outbound.wxml -->
<view class="outbound-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#2c9678" size="24px" vertical>
    加载中...
  </van-loading>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 顶部：日期选择区域 -->
    <view class="date-selection-section">
      <view class="date-header">
        <view class="date-title">
          <van-icon name="calendar-o" size="20px" color="#2c9678" />
          <text>选择出库日期</text>
        </view>
        <view class="selected-date-display">
          {{selectedDate || '请选择日期'}}
        </view>
      </view>
      <!-- 日历组件 -->
      <view class="calendar-wrapper">
        <van-calendar
          show="{{showCalendar}}"
          type="single"
          color="#2c9678"
          bind:confirm="onDateConfirm"
          bind:close="onCalendarClose"
          min-date="{{minDate}}"
          max-date="{{maxDate}}"
          default-date="{{defaultDate}}"
        />
        <van-button
          type="primary"
          size="small"
          custom-class="date-select-btn"
          bind:click="onShowCalendar"
        >
          选择日期
        </van-button>
      </view>
    </view>

    <!-- 中部：出库记录列表展示区域 -->
    <view class="outbound-records-section">
      <view class="records-header">
        <view class="records-title">
          <van-icon name="orders-o" size="20px" color="#2c9678" />
          <text>出库记录</text>
        </view>
        <view class="records-stats">
          <text>共 {{outboundRecords.length}} 条记录</text>
        </view>
      </view>

      <!-- 出库记录列表 -->
      <scroll-view class="records-list" scroll-y show-scrollbar="{{false}}">
        <view wx:if="{{outboundRecords.length === 0}}" class="empty-state">
          <van-empty description="暂无出库记录" />
        </view>
        <view wx:else>
          <view wx:for="{{outboundRecords}}" wx:key="id" class="record-card">
            <!-- 卡片头部 -->
            <view class="card-header">
              <view class="card-title">
                <view class="warehouse-name">{{item.warehouse_name}}</view>
                <view class="record-time">{{item.created_time}}</view>
              </view>
              <view class="card-actions">
                <van-icon name="edit" size="16px" color="#2c9678" data-record="{{item}}" bind:click="onEditRecord" />
                <van-icon name="delete" size="16px" color="#ee0a24" data-record="{{item}}" bind:click="onDeleteRecord" />
              </view>
            </view>

            <!-- 卡片内容 -->
            <view class="card-content">
              <!-- 服装信息 -->
              <view class="clothing-info-section">
                <view wx:for="{{item.contents}}" wx:for-item="content" wx:key="sku" class="clothing-item">
                  <view class="clothing-name clickable"
                        style="{{content.clothing_id ? 'color: #2c9678' : 'color: #333'}}"
                        data-content="{{content}}"
                        bind:click="onClothingNameTap">
                    {{content.name}}
                  </view>
                  <view class="clothing-quantity">{{content.outbound_quantity}}件</view>
                </view>
              </view>

              <!-- 统计信息 -->
              <view class="summary-info">
                <view class="summary-item">
                  <text class="summary-label">总包数：</text>
                  <text class="summary-value">{{item.total_packages}}包</text>
                </view>
                <view class="summary-item">
                  <text class="summary-label">总件数：</text>
                  <text class="summary-value">{{item.total_pieces}}件</text>
                </view>
                <view class="summary-item">
                  <text class="summary-label">操作人：</text>
                  <text class="summary-value">{{item.operator_name}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
  <!-- 底部：操作按钮区域 -->
  <view class="bottom-actions-section">
    <view class="actions-container">
      <!-- 新建出库记录按钮 -->
      <van-button
        type="primary"
        size="large"
        custom-class="action-button primary-button"
        bind:click="onCreateOutbound"
      >
        <van-icon name="plus" />
        新建出库
      </van-button>

      <!-- 批量操作按钮 -->
      <van-button
        type="default"
        size="large"
        custom-class="action-button secondary-button"
        bind:click="onBatchOperation"
        disabled="{{outboundRecords.length === 0}}"
      >
        <van-icon name="setting-o" />
        批量操作
      </van-button>

      <!-- 导出记录按钮 -->
      <van-button
        type="default"
        size="large"
        custom-class="action-button secondary-button"
        bind:click="onExportRecords"
        disabled="{{outboundRecords.length === 0}}"
      >
        <van-icon name="download" />
        导出记录
      </van-button>
    </view>
  </view>

  <!-- 服装信息弹窗 -->
  <van-popup
    show="{{ showClothingInfo }}"
    round
    position="center"
    custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;"
    bind:close="closeClothingInfo"
  >
    <view class="clothing-popup-container">
      <clothing-info-card
        clothingInfo="{{ selectedClothingInfo.is_oem ? null : selectedClothingInfo }}"
        oemClothingInfo="{{ selectedClothingInfo.is_oem ? selectedClothingInfo : null }}"
        isOem="{{ selectedClothingInfo.is_oem }}"
      />
    </view>
  </van-popup>

  <!-- 编辑出库记录弹窗 -->
  <van-popup
    show="{{ showEditDialog }}"
    round
    position="center"
    custom-style="width: 90%; max-width: 500rpx; border-radius: 16rpx;"
    bind:close="onCloseEditDialog"
  >
    <view class="edit-dialog">
      <view class="dialog-header">
        <view class="dialog-title">编辑出库记录</view>
        <van-icon name="cross" size="20px" color="#999" bind:click="onCloseEditDialog" />
      </view>
      <view class="dialog-content">
        <!-- 编辑表单内容 -->
        <view class="form-item">
          <view class="form-label">出库日期：</view>
          <view class="form-value">{{editingRecord.outbound_date}}</view>
        </view>
        <view class="form-item">
          <view class="form-label">仓库：</view>
          <view class="form-value">{{editingRecord.warehouse_name}}</view>
        </view>
        <!-- 更多编辑字段... -->
      </view>
      <view class="dialog-actions">
        <van-button type="default" size="small" bind:click="onCloseEditDialog">取消</van-button>
        <van-button type="primary" size="small" bind:click="onSaveEdit">保存</van-button>
      </view>
    </view>
  </van-popup>

  <!-- 删除确认弹窗 -->
  <van-dialog
    show="{{ showDeleteDialog }}"
    title="确认删除"
    message="确定要删除这条出库记录吗？删除后无法恢复。"
    show-cancel-button
    bind:confirm="onConfirmDelete"
    bind:cancel="onCancelDelete"
  />
</view>