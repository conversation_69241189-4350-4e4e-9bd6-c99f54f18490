// pages/outbound/outbound.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 日期选择相关
    showCalendar: false,
    selectedDate: '',
    minDate: new Date(2020, 0, 1).getTime(),
    maxDate: new Date(2030, 11, 31).getTime(),
    defaultDate: new Date().getTime(),

    // 出库记录列表
    outboundRecords: [],

    // 弹窗状态
    showClothingInfo: false,
    showEditDialog: false,
    showDeleteDialog: false,

    // 当前操作的记录
    selectedClothingInfo: null,
    editingRecord: null,
    deletingRecord: null,

    // 加载状态
    loading: false,
    loadingRecords: false,
  },

  // 定时器引用，用于清理
  _timers: [],

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 初始化页面数据
    this.initializePage();

    // 加载出库记录
    this.loadOutboundRecords();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新出库记录
    this.loadOutboundRecords();
  },

  /**
   * 初始化页面
   */
  initializePage() {
    // 设置默认选中今天的日期
    const today = new Date();
    const todayStr = this.formatDate(today);

    this.setData({
      selectedDate: todayStr,
      defaultDate: today.getTime()
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 加载出库记录
   */
  async loadOutboundRecords() {
    try {
      this.setData({ loadingRecords: true });

      const { selectedDate } = this.data;
      if (!selectedDate) {
        this.setData({
          outboundRecords: [],
          loadingRecords: false
        });
        return;
      }

      // 调用API获取出库记录
      const response = await Api.getOutboundRecords({
        date: selectedDate,
        limit: 1000
      });

      if (response.data.code === 200) {
        const records = response.data.data.list || [];

        // 处理记录数据，添加格式化时间等
        const processedRecords = records.map(record => ({
          ...record,
          created_time: this.formatDateTime(record.created_at),
          // 确保contents是数组格式
          contents: Array.isArray(record.contents) ? record.contents : []
        }));

        this.setData({
          outboundRecords: processedRecords,
          loadingRecords: false
        });
      } else {
        console.error("加载出库记录失败:", response.data.message);
        wx.showToast({
          title: response.data.message || "加载记录失败",
          icon: "none",
        });
        this.setData({ loadingRecords: false });
      }
    } catch (error) {
      console.error("加载出库记录异常:", error);
      wx.showToast({
        title: "加载记录失败",
        icon: "none",
      });
      this.setData({ loadingRecords: false });
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';

    const date = new Date(dateTimeStr);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${hours}:${minutes}`;
  },

  /**
   * 显示日历
   */
  onShowCalendar() {
    this.setData({ showCalendar: true });
  },

  /**
   * 关闭日历
   */
  onCalendarClose() {
    this.setData({ showCalendar: false });
  },

  /**
   * 确认选择日期
   */
  onDateConfirm(e) {
    const selectedTimestamp = e.detail;
    const selectedDate = new Date(selectedTimestamp);
    const dateStr = this.formatDate(selectedDate);

    this.setData({
      selectedDate: dateStr,
      showCalendar: false
    });

    // 重新加载该日期的出库记录
    this.loadOutboundRecords();
  },

  /**
   * 点击服装名称
   */
  async onClothingNameTap(e) {
    const { content } = e.currentTarget.dataset;

    // 显示加载状态
    wx.showLoading({
      title: "加载中...",
      mask: true,
    });

    try {
      let clothingInfo;

      if (content.is_oem) {
        // 获取OEM服装详细信息
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: content.oem_clothing_id || content.clothing_id,
        });

        if (response.data && response.data.code === 200) {
          const oemData = response.data.data;
          clothingInfo = {
            ...oemData,
            is_oem: true,
          };
        } else {
          throw new Error("获取OEM服装信息失败");
        }
      } else {
        // 获取普通服装详细信息
        const response = await Api.getClothingInfo({
          clothing_id: content.clothing_id,
        });

        if (response.data && response.data.code === 200) {
          const clothingData = response.data.data;
          clothingInfo = {
            ...clothingData,
            is_oem: false,
          };
        } else {
          throw new Error("获取服装信息失败");
        }
      }

      this.setData({
        selectedClothingInfo: clothingInfo,
        showClothingInfo: true,
      });
    } catch (error) {
      console.error("获取服装详细信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  closeClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
    });
  },

  /**
   * 新建出库记录
   */
  onCreateOutbound() {
    // 跳转到新建出库页面（原来的出库页面逻辑）
    wx.navigateTo({
      url: '/pages/outbound-create/outbound-create'
    });
  },

  /**
   * 编辑出库记录
   */
  onEditRecord(e) {
    const { record } = e.currentTarget.dataset;

    this.setData({
      editingRecord: record,
      showEditDialog: true
    });
  },

  /**
   * 删除出库记录
   */
  onDeleteRecord(e) {
    const { record } = e.currentTarget.dataset;

    this.setData({
      deletingRecord: record,
      showDeleteDialog: true
    });
  },

  /**
   * 关闭编辑弹窗
   */
  onCloseEditDialog() {
    this.setData({
      showEditDialog: false,
      editingRecord: null
    });
  },

  /**
   * 保存编辑
   */
  async onSaveEdit() {
    // TODO: 实现编辑保存逻辑
    wx.showToast({
      title: "编辑功能开发中",
      icon: "none"
    });

    this.onCloseEditDialog();
  },

  /**
   * 确认删除
   */
  async onConfirmDelete() {
    try {
      const { deletingRecord } = this.data;

      // 调用删除API
      const response = await Api.deleteOutboundRecord({
        record_id: deletingRecord.id
      });

      if (response.data.code === 200) {
        wx.showToast({
          title: "删除成功",
          icon: "success"
        });

        // 重新加载记录
        this.loadOutboundRecords();
      } else {
        wx.showToast({
          title: response.data.message || "删除失败",
          icon: "none"
        });
      }
    } catch (error) {
      console.error("删除出库记录失败:", error);
      wx.showToast({
        title: "删除失败",
        icon: "none"
      });
    }

    this.setData({
      showDeleteDialog: false,
      deletingRecord: null
    });
  },

  /**
   * 取消删除
   */
  onCancelDelete() {
    this.setData({
      showDeleteDialog: false,
      deletingRecord: null
    });
  },

  /**
   * 批量操作
   */
  onBatchOperation() {
    wx.showToast({
      title: "批量操作功能开发中",
      icon: "none"
    });
  },

  /**
   * 导出记录
   */
  onExportRecords() {
    wx.showToast({
      title: "导出功能开发中",
      icon: "none"
    });
  },

});
