/* pages/outbound-create/outbound-create.wxss */

/* ==================== 基础布局 ==================== */
.outbound-container {
  margin-top: 10rpx;
  padding: 0 20rpx 160rpx 20rpx;
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  flex: 1;
  min-height: 0;
  overflow: visible;
}

/* ==================== 搜索卡片 ==================== */
.search-section {
  background-color: #3563632c;
  box-shadow: 0 10px 6px -6px rgba(30, 30, 30, 0.1),
    12px 0 8px -8px rgba(50, 50, 50, 0.1);
  border-radius: 10rpx;
  overflow: visible;
  flex-shrink: 0;
}

.search-header {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1rpx solid #efefef;
  flex-shrink: 0;
}

.search-form {
  display: flex;
  gap: 12rpx;
  align-items: center;
  flex-wrap: wrap;
}

.search-form-item {
  flex: 1;
  min-width: 0;
}

.search-input-container {
  position: relative;
  z-index: 100;
}

.search-form-item:nth-child(1) {
  flex: 2;
}

.search-form-item:nth-child(2),
.search-form-item:nth-child(3) {
  flex: 0.8;
}

.search-form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
  line-height: 80rpx;
}

.search-form-input:focus {
  border-color: #2c9678;
  background: rgba(44, 150, 120, 0.05);
}

.search-form-btn, .reset-form-btn {
  width: 100% !important;
  height: 80rpx !important;
  border-radius: 8rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
}

.search-form-btn {
  background: linear-gradient(135deg, #2c9678 0%, #3ba688 100%) !important;
  border: none !important;
  color: white !important;
}

.reset-form-btn {
  background: #f5f5f5 !important;
  color: #666 !important;
  border: 1rpx solid #e0e0e0 !important;
}

/* ==================== 智能搜索建议样式 ==================== */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1rpx solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8rpx 8rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  z-index: 9999;
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  gap: 12rpx;
  color: #999;
  font-size: 24rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
  min-height: 60rpx;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: #f0f9f6;
}

.suggestion-name {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 左右分栏布局 ==================== */
.split-layout {
  display: flex;
  gap: 20rpx;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.left-panel,
.right-panel {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.left-panel {
  flex: 0 0 200rpx;
  min-width: 200rpx;
}

.right-panel {
  flex: 1;
}

.panel-header {
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  height: 46rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.panel-stats {
  display: flex;
  align-items: center;
}

.stats-view {
  font-size: 24rpx;
  color: #2c9678;
  font-weight: 600;
  background: #e8f5f1;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* ==================== 仓库列表 ==================== */
.warehouse-list {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.warehouse-item {
  padding: 10rpx 20rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s;
}

.warehouse-item:active {
  background-color: #f0f0f0;
}

.warehouse-item.selected {
  background-color: #e8f5f1;
  border-left: 4rpx solid #2c9678;
}

.warehouse-info {
  flex: 1;
  cursor: pointer;
}

.warehouse-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

.warehouse-address {
  font-size: 22rpx;
  color: #666;
  margin-top: 6rpx;
  display: block;
}

/* ==================== 库存明细列表 ==================== */
.details-list {
  flex: 1;
  padding: 20rpx;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.detail-item {
  background: white;
  border-radius: 10rpx;
  padding: 8rpx;
  margin-bottom: 8rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.item-content {
  margin-bottom: 0;
}

.empty-state {
  padding: 60rpx 0;
  text-align: center;
}

/* ==================== 服装信息布局 ==================== */
.item-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  gap: 12rpx;
}

.clothing-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
  gap: 4rpx;
}

.clothing-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clothing-name.clickable {
  color: #2c9678;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #1e6b5c;
}

.stock-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.stock-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

/* ==================== 操作区域 ==================== */
.item-actions-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-top: 6rpx;
  padding: 0 12rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border: 1rpx solid #e0f2fe;
  min-height: 80rpx;
}

.stock-quantity-group {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
  min-width: 0;
}

.stock-display {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  flex-shrink: 0;
  min-width: 80rpx;
}

.stock-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #ff9800;
  text-align: center;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 200rpx;
}

.outbound-button-area {
  flex-shrink: 0;
  width: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-tips {
  text-align: center;
  padding: 16rpx;
}

.tip-view {
  font-size: 24rpx;
  color: #999;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
  font-weight: 600;
}

/* ==================== 多货物包裹卡片样式 ==================== */
.multi-item-list {
  margin-bottom: 10rpx;
}

.multi-item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0rpx 12rpx;
  margin-bottom: 6rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.multi-item-row:last-child {
  margin-bottom: 0;
}

.multi-item-info {
  flex: 2;
  display: flex;
  align-items: center;
  gap: 12rpx;
  min-width: 0;
}

.multi-item-quantity {
  flex: 0 0 auto;
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
}

.multi-item-input {
  flex: 0 0 auto;
  width: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.multi-input-field {
  text-align: center !important;
  font-size: 30rpx !important;
  font-weight: 600 !important;
  height: 40rpx !important;
  border: 2rpx solid #ddd !important;
  border-radius: 10rpx !important;
  box-sizing: border-box !important;
  background: #fff !important;
  color: #333 !important;
}

.multi-item-input .van-cell {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.multi-item-input .van-field__control {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.multi-item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 12rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border: 1rpx solid #e0f2fe;
  margin-top: 6rpx;
}

.package-calculation {
  flex: 1;
}

.calculation-display {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff9800;
}

/* ==================== 底部操作栏 ==================== */
.bottom-fixed-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  z-index: 1000;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  align-items: center;
}

.action-item {
  flex: 1;
  max-width: 300rpx;
}

.action-button {
  width: 100% !important;
  border-radius: 12rpx !important;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.action-button[type="success"] {
  background: linear-gradient(135deg, #2c9678 0%, #3ba688 100%) !important;
  box-shadow: 0 4rpx 12rpx rgba(44, 150, 120, 0.3) !important;
  border: none !important;
  color: white !important;
}

.action-button[type="success"]:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(44, 150, 120, 0.4) !important;
}

/* ==================== 弹窗样式 ==================== */
.clothing-popup-container {
  width: 100%;
  background: transparent;
}
