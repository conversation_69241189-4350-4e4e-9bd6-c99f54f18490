<!-- pages/outbound-create/outbound-create.wxml -->
<view class="outbound-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#2c9678" size="24px" vertical>
    加载中...
  </van-loading>
  
  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 搜索卡片 -->
    <view class="search-section">
      <view class="search-header">
        <view class="search-form">
          <!-- 服装名称输入框 -->
          <view class="search-form-item search-input-container">
            <input 
              id="search-input" 
              class="search-form-input" 
              value="{{searchKeyword}}" 
              placeholder="输入服装名称搜索库存" 
              focus="{{searchInputFocused}}" 
              bindinput="onSearchInput" 
              bindfocus="onSearchFocus" 
              bindblur="onSearchBlur" 
              bindtap="onSearchClick" 
            />
            <!-- 智能下拉提示 -->
            <view wx:if="{{showSearchSuggestions}}" class="search-suggestions">
              <view wx:if="{{loadingSuggestions}}" class="suggestion-loading">
                <van-loading type="spinner" size="16px" />
                <text>搜索中...</text>
              </view>
              <view wx:else>
                <view 
                  wx:for="{{searchSuggestions}}" 
                  wx:key="name" 
                  class="suggestion-item" 
                  data-suggestion="{{item}}" 
                  bindtap="onSuggestionTap" 
                  bindtouchmove="onSuggestionTouchMove"
                >
                  <view class="suggestion-name">{{item.name}}</view>
                  <van-icon name="arrow" size="14px" color="#999" />
                </view>
              </view>
            </view>
          </view>
          <!-- 搜索按钮 -->
          <view class="search-form-item">
            <van-button 
              size="small" 
              type="primary" 
              bind:click="onSearch" 
              loading="{{searchLoading}}" 
              disabled="{{searchLoading}}" 
              custom-class="search-form-btn"
            >
              搜索
            </van-button>
          </view>
          <!-- 重置按钮 -->
          <view class="search-form-item">
            <van-button 
              size="small" 
              bind:click="onResetSearch" 
              custom-class="reset-form-btn"
            >
              重置
            </van-button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 左右分栏布局 -->
    <view class="split-layout">
      <!-- 左侧：仓库列表 -->
      <view class="left-panel">
        <view class="panel-header">
          <view class="panel-title">{{isSearchMode ? '搜索结果' : '仓库列表'}}</view>
        </view>
        <scroll-view class="warehouse-list" scroll-y show-scrollbar="{{false}}">
          <view 
            wx:for="{{warehouseList}}" 
            wx:key="warehouse_id" 
            class="warehouse-item {{selectedWarehouse && selectedWarehouse.warehouse_id === item.warehouse_id ? 'selected' : ''}}"
          >
            <view class="warehouse-info" data-warehouse="{{item}}" bind:tap="onSelectWarehouse">
              <view class="warehouse-name">{{item.name}}</view>
              <view class="warehouse-address">{{item.address}}</view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 右侧：库存明细列表 -->
      <view class="right-panel">
        <view class="panel-header">
          <view class="panel-title">库存明细</view>
          <view class="panel-stats">
            <view class="stats-view">{{totalPackages || 0}}包 / {{totalPieces || 0}}件</view>
          </view>
        </view>
        <view wx:if="{{inventoryList.length === 0}}" class="empty-state">
          <van-empty description="暂无库存数据" />
        </view>
        <view wx:else class="details-list">
          <view 
            wx:for="{{inventoryList}}" 
            wx:for-index="inventoryIndex" 
            wx:key="unique_id" 
            class="detail-item"
          >
            <!-- 单货物包裹卡片 -->
            <view wx:if="{{item.contents.length === 1}}" class="item-content single-item-card">
              <!-- 主要信息横向布局 -->
              <view class="item-main-info">
                <!-- 左侧：服装信息 -->
                <view class="clothing-info">
                  <view 
                    class="clothing-name clickable" 
                    style="{{item.contents[0].clothing_id ? 'color: #2c9678' : 'color: #000'}}" 
                    data-content="{{item.contents[0]}}" 
                    bindtap="onClothingNameTap"
                  >
                    {{item.contents[0].name || '未知服装'}}
                  </view>
                </view>
                <!-- 右侧：库存信息 -->
                <view class="stock-info">
                  <view class="stock-formula">
                    {{item.availableTotalQuantity !== undefined ? item.availableTotalQuantity : item.total_quantity || 0}}件   ({{item.contents[0].original_quantity || 0}}件/包)
                  </view>
                </view>
              </view>
              
              <!-- 操作区域：库存数量、步进器、出库按钮在同一排 -->
              <view 
                class="item-actions-row" 
                wx:if="{{(item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count) > 0 && selectedWarehouse}}"
              >
                <!-- 固定位置的数量显示和控制组 -->
                <view class="stock-quantity-group">
                  <view class="stock-display">
                    <view class="stock-value">
                      {{item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count}}包
                    </view>
                  </view>
                  <view class="quantity-control">
                    <van-stepper 
                      value="{{item.selectedPackageQuantity || 0}}" 
                      min="0" 
                      max="{{item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count}}" 
                      step="0.5" 
                      data-inventory-index="{{inventoryIndex}}" 
                      bind:change="onSingleItemQuantityChange" 
                    />
                  </view>
                </view>
                <!-- 出库按钮区域 -->
                <view class="outbound-button-area">
                  <van-button 
                    wx:if="{{item.selectedPackageQuantity && item.selectedPackageQuantity > 0}}" 
                    type="primary" 
                    size="small" 
                    data-inventory-index="{{inventoryIndex}}" 
                    bind:click="onSingleItemOutbound"
                  >
                    出库
                  </van-button>
                </view>
              </view>
              
              <!-- 提示信息 -->
              <view 
                class="item-tips" 
                wx:if="{{(item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count) > 0 && !selectedWarehouse}}"
              >
                <view class="tip-view">请先选择仓库</view>
              </view>
              <view 
                class="item-tips" 
                wx:if="{{(item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count) <= 0}}"
              >
                <view class="empty-tip">暂无库存</view>
              </view>
            </view>
            
            <!-- 多货物包裹卡片 -->
            <view wx:else class="item-content">
              <!-- 货物列表 -->
              <view class="multi-item-list">
                <view 
                  wx:for="{{item.contents}}" 
                  wx:for-item="content" 
                  wx:for-index="contentIndex" 
                  wx:key="sku" 
                  class="multi-item-row"
                >
                  <!-- 左侧：服装信息 -->
                  <view class="multi-item-info">
                    <view 
                      class="clothing-name clickable" 
                      style="{{content.clothing_id ? 'color: #2c9678' : 'color: #000'}}" 
                      data-content="{{content}}" 
                      bindtap="onClothingNameTap"
                    >
                      {{content.name}}
                    </view>
                    <view class="multi-item-quantity">
                      {{content.available_quantity !== undefined ? content.available_quantity : content.current_quantity}}件
                    </view>
                  </view>
                  <!-- 右侧：数字输入框 -->
                  <view class="multi-item-input">
                    <van-field 
                      value="{{content.selectedQuantity || 0}}" 
                      type="number" 
                      placeholder="0" 
                      data-inventory-index="{{inventoryIndex}}" 
                      data-content-index="{{contentIndex}}" 
                      bind:change="onMultiItemQuantityChange" 
                      input-class="multi-input-field" 
                    />
                  </view>
                </view>
              </view>
              
              <!-- 出货包数计算和出库按钮 -->
              <view class="multi-item-footer" wx:if="{{selectedWarehouse}}">
                <view class="package-calculation">
                  <view class="calculation-display">
                    出库包数: {{item.calculatedPackageQuantity || '0'}}包
                  </view>
                </view>
                <van-button 
                  wx:if="{{item.calculatedPackageQuantity && item.calculatedPackageQuantity > 0}}" 
                  type="primary" 
                  size="small" 
                  data-inventory-index="{{inventoryIndex}}" 
                  bind:click="onMultiItemOutbound"
                >
                  出库
                </van-button>
              </view>
              
              <!-- 提示信息 -->
              <view class="item-tips" wx:if="{{!selectedWarehouse}}">
                <view class="tip-view">请先选择仓库</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部固定操作栏 -->
  <view class="bottom-fixed-bar">
    <view class="bottom-actions">
      <!-- 待出库清单按钮 -->
      <view class="action-item">
        <van-button 
          type="info" 
          size="large" 
          bind:click="toggleCart" 
          class="action-button" 
          disabled="{{!outboundList || outboundList.length === 0}}"
        >
          <van-icon name="shopping-cart-o" />
          待出库 ({{cartTotalPackages}}包)
        </van-button>
      </view>
      <!-- 核对按钮 -->
      <view class="action-item">
        <van-button 
          type="success" 
          size="large" 
          bind:click="confirmOutbound" 
          class="action-button" 
          disabled="{{!outboundList || outboundList.length === 0}}"
        >
          <van-icon name="passed" />
          核对
        </van-button>
      </view>
    </view>
  </view>
  
  <!-- 服装信息弹窗 -->
  <van-popup 
    show="{{ showClothingInfo }}" 
    round 
    position="center" 
    custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;" 
    bind:close="closeClothingInfo"
  >
    <view class="clothing-popup-container">
      <clothing-info-card 
        clothingInfo="{{ selectedClothingInfo.is_oem ? null : selectedClothingInfo }}" 
        oemClothingInfo="{{ selectedClothingInfo.is_oem ? selectedClothingInfo : null }}" 
        isOem="{{ selectedClothingInfo.is_oem }}" 
      />
    </view>
  </van-popup>
</view>
